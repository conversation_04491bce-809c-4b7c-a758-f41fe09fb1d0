import { defineConfig, loadEnv } from 'vite'
// let baseURL = "https://invilms.ezbricks.in/api";
// let baseURL = "https://raghava.ezbricks.in/api";
//let baseURL = "http://localhost:3000/#/";
 // let baseURL = "https://pmcdcn.ezbricks.in/";
  // let baseURL = "https://pharma.ezbricks.in/";
   let baseURL = "https://pmcrms.justservices.in/";


if (
  process.env.NODE_ENV &&
  process.env.NODE_ENV.toLowerCase() === "production"
) {
  baseURL = "/";
}
export const loginUrl = "api/authentication/token";
export const forgotpasswordsUrl = "api/User/ForgotPassword";
export const createPasswordUrl = "api/User/ResetPassword";
export const application = "api/application";
export const applicationRole = "api/Role";
export const apiscope = "api/apiscope";
export const apimeta = "api/apimeta";
export const apiclient = "api/apiclient";
export const userinvitation = "api/Invitation";
export const account = "api/account";
export const mastermeta = "api/mastermeta";
export const masterdata = "api/masterdata";
export const entity = "api/entity";
export const entityMeta = "api/entitymeta";
export const user = "api/user";
export const forms = "api/forms";
export const myForms = "api/Forms/myforms";
export const entitytask = "api/entitytask";
export const valuemeta = "api/valuemeta";
export const applicationUser = "api/User";
export const file = "api/file";

// plugin
export const pluginNotification = "api/plugin/notification";
export const pluginRemoteCall = "api/plugin/remotecall";

// Remote Call Plugin
export const remoteCallPlugin = "api/remotecallplugin";

// notificationsPlugin
export const notificationsPlugin = "api/notificationplugin";
export const configuration = "api/configuration";

// dashboard report
export const report = "api/report";
export const reportme = "api/report/me";
// Entity List
export const entityList = "api/entitylist"
//Template
export const template = 'api/Template'
export const mvc = 'MVC'
//logs
export const logger="api/Logger";
//Geust
export const guestLogin = "api/guest";
export const vanityForm = "api/Forms/vanity/";
// task
export const taskView = "api/taskView"
export const drillDownReport = "api/DrillDownReport"
export const uiBundles = "api/Ui/UiBundle"
export const backgroundHandler = "api/BackgroundHandler"
export const emailConfiguration = "api/EmailConfiguration"
export const saveEntity = "api/SaveEntity"
export const scheduleHandler = "api/scheduledHandler"
export const notification = "api/notification"
export const userMeta = "api/UserMeta"
export {
  baseURL
};
